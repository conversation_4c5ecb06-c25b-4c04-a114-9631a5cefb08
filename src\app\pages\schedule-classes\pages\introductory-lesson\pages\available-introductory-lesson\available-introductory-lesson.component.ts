import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatNativeDateModule, provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { MatIconModule } from '@angular/material/icon';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatButtonModule } from '@angular/material/button';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse } from 'src/app/shared/models';
import moment from 'moment';
import { SharedModule } from 'src/app/shared/shared.module';
import { MbscDatepickerModule } from '@mobiscroll/angular';
import { MatInputModule } from '@angular/material/input';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { AvailableLesson, InstructorBioDetail, IntroductoryLessonDetail, SelectedLessonInfo } from '../../models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { Duration } from 'src/app/pages/settings/pages/plan/models';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    CommonModule,
    MatIconModule,
    OverlayModule,
    MatButtonModule,
    SharedModule,
    MbscDatepickerModule,
    MatInputModule,
    MatSidenavModule
  ]
};

@Component({
  selector: 'app-available-introductory-lesson',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './available-introductory-lesson.component.html',
  styleUrl: './available-introductory-lesson.component.scss'
})
export class AvailableIntroductoryLessonComponent extends BaseComponent implements OnInit {
  @Input() scheduleInfo!: SelectedLessonInfo;
  @Input() selectedInstructorsIdFromParent!: Array<number> | undefined;
  @Input() isScheduleMakeUpLesson!: boolean;
  @Input() isPassReschedule!: boolean;

  isInstructorsSideNavOpen = false;
  showInstructorDetailsFlag = false;
  dateRange: { date: string }[] = [];
  appointments!: AvailableLesson[];
  currentDate = new Date();
  totalInstructorsCount!: number;

  startDate!: Date;
  endDate!: Date;
  startTime!: Date;
  endTime!: Date;
  selectedDate!: string;
  selectedTime!: string;
  selectedTimeSlotId!: number;

  @Output() scheduleAppointmentsDetails = new EventEmitter<InstructorBioDetail>();
  @Output() toggleInstructorSideNav = new EventEmitter<boolean>();

  constructor(
    private readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef,
    private readonly instructorService: InstructorService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeDates();
    this.getInstructorsCount();
  }

  initializeDates(): void {
    this.startDate = this.currentDate;
    this.endDate = this.getWeekEndDate();
    this.getDatesBetween(this.startDate, this.endDate);
    this.getInstructorsCount();
  }

  getIntroductoryLessonDetails(selectedInstructorsId?: Array<number>): void {
    const instructorIds = this.isPassReschedule
      ? [this.scheduleInfo.instructorId]
      : selectedInstructorsId ?? this.selectedInstructorsIdFromParent;

    const scheduleDate = this.selectedDate || new Date().toISOString();

    this.showPageLoader = true;
    this.schedulerService
      .add(
        {
          scheduleStartDate: DateUtils.getUtcRangeForLocalDate(scheduleDate).startUtc,
          scheduleEndDate: DateUtils.getUtcRangeForLocalDate(scheduleDate).endUtc,
          locationId: this.scheduleInfo?.locationId,
          instrumentId: this.scheduleInfo?.instrumentId,
          duration: this.scheduleInfo?.duration ?? Duration.THIRTY,
          isIntroductoryClassAvailable: this.scheduleInfo.classType === ClassTypes.INTRODUCTORY,
          instructorIdFilter: instructorIds,
          startTimeFilter: CommonUtils.combineDateAndTime(scheduleDate, this.startTime?.toISOString()),
          endTimeFilter: CommonUtils.combineDateAndTime(scheduleDate, this.endTime?.toISOString()),
          studentId: this.scheduleInfo?.studentId
        },
        API_URL.scheduleLessonDetails.getAvailableIntroductoryLessons
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<AvailableLesson>) => {
          this.appointments = res.result.items.map(item => {
            const isToday = moment(item.scheduleDate).isSame(moment(), 'day');

            const filteredDetails = item.introductoryLessonsDetails
              .map(detail => ({
                ...detail,
                startTime: DateUtils.toLocal(detail.startTime, 'yyyy-MM-DDTHH:mm:ss'),
                endTime: DateUtils.toLocal(detail.endTime, 'yyyy-MM-DDTHH:mm:ss')
              }))
              .filter(detail => {
                if (!isToday) {
                  return true; 
                } 
                return CommonUtils.isFutureLesson(detail.startTime);
              });

            return {
              ...item,
              introductoryLessonsDetails: filteredDetails
            };
          });

          this.setSelectedSlotIdAndShowInstructorDetailsFlag(this.appointments[0]?.introductoryLessonsDetails[0], false);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      LocationIdFilter: [this.scheduleInfo.locationId],
      InstrumentIdFilter: [this.scheduleInfo.instrumentId],
      Page: 1
    });
  }

  getInstructorsCount(): void {
    this.instructorService
      .add(this.getInstructorFilterParams(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: CBResponse<InstructorList>) => {
          this.totalInstructorsCount = response.result.totalCount;
          this.cdr.detectChanges();
        }
      });
  }

  onDateRangeSelected(): void {
    if (this.startDate && this.endDate) {
      this.dateRange = [];
      this.getDatesBetween(this.startDate, this.endDate);
    }
  }

  getDatesBetween(startDate: Date, endDate: Date): void {
    let currentDate = moment(startDate);
    const lastDate = moment(endDate);

    while (currentDate <= lastDate) {
      this.dateRange.push({
        date: currentDate.toString()
      });
      currentDate = currentDate.add(1, 'day');
    }
    this.selectedDate = this.dateRange[0].date;
    this.getIntroductoryLessonDetails();
  }

  setTimeFilter(event: any): void {
    this.selectedTime = event.valueText;
    [this.startTime, this.endTime] = event.value;
    this.getIntroductoryLessonDetails();
  }

  setScheduleDate(date: string): void {
    this.selectedDate = date;
    this.getIntroductoryLessonDetails();
  }

  getWeekEndDate(): Date {
    return moment().add(6, 'days').toDate();
  }

  setSelectedSlotIdAndShowInstructorDetailsFlag(slotDetails: IntroductoryLessonDetail, flag: boolean, event?: MouseEvent): void {
    if (flag) {
      event?.stopPropagation();
    }
    this.selectedTimeSlotId = slotDetails?.id;
    this.showInstructorDetailsFlag = flag;
    this.scheduleAppointmentsDetails.emit({
      slotDetails,
      showStaffDetails: flag,
      dateRange: { startDate: this.startDate, endDate: this.endDate }
    });
  }

  openInstructorSideNav(isOpen: boolean): void {
    this.toggleInstructorSideNav.emit(isOpen);
  }
}
