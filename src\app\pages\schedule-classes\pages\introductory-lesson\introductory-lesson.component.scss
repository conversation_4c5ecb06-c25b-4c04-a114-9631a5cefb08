@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.auth-page-with-header {
  height: calc(100vh - 105px);

  .o-card {
    padding: 0px;

    .o-card-body {
      .schedule-introductory-lesson-content-wrapper {
        overflow-x: hidden;
        border-radius: 12px;

        .content-detail-wrapper::-webkit-scrollbar {
          display: none;
        }

        .content-detail-wrapper {
          padding: 20px 10px 20px 35px;
          height: calc(100vh - 150px);
          overflow: auto;

          p {
            color: $gray-text;
            font-weight: 400;
            font-size: 16px;
          }

          .introductory-detail-wrapper {
            .schedule-information-title {
              @include flex-content-space-between;
              font-size: 16px;
              font-weight: 700;
              padding-bottom: 10px;
            }

            .schedule-basic-details {
              background: $header-schedule-bg-color;
              padding: 12px 16px;
              margin: 8px 0;
              border-radius: 6px;

              .group-name-age {
                font-size: 16px;
                font-weight: 700;
              }

              .location,
              .dependent-name {
                @include flex-content-align-center;
                font-size: 14px;

                .location-icon,
                .dependent-icon {
                  height: 15px;
                  width: 15px;
                }

                .location-info-text,
                .dependent-name {
                  margin: 0 4px;

                  .name {
                    margin-left: 4px;
                    font-weight: 700;
                  }
                }
              }

              .location {
                margin: 3px 0;
              }
            }

            .appointment-detail {
              margin: 25px 0px;

              .appointment-detail-content {
                @include flex-content-align-center;
                margin-bottom: 10px;

                img {
                  height: 21px;
                  width: 22px;
                }

                .appointment-info {
                  font-size: 16px;
                  font-weight: 700;
                  color: $black-shade-text;
                  margin-left: 10px;
                }
              }
            }

            .schedule-info-wrapper {
              @include flex-content-space-between;
              margin-top: 20px;
              font-size: 16px;

              .text-content {
                font-weight: 700;
                font-size: 16px;
              }

              .update-info-btn {
                color: $primary-color;
                cursor: pointer;
                font-weight: 700;
              }
            }

            .course-desc-wrapper {
              color: $gray-text;
            }

            .staff-img {
              margin-top: 10px;

              img {
                height: 240px;
                width: 420px;
                border-radius: 10px;
                max-width: -webkit-fill-available;
              }
            }

            .staff-name {
              margin: 20px 0px 10px 0px;
              font-weight: 600;
              color: $un-auth-title-color;
            }

            .description {
              margin: 8px 0;
            }
          }
        }

        .schedule-introductory-info-form-wrapper {
          box-shadow: 0px 4px 50px 0px #0000000d;
          border-left: 1px solid $btn-options-border-color;
          height: 100%;
          padding: 20px;

          .introductory-lesson-wrapper {
            padding-right: 10px;
            height: calc(100vh - 185px);
            overflow: auto;
          }
        }
      }
    }
  }
}

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }

  .mat-error-position {
    position: relative;
  }
}

.field-content {
  @include flex-content-align-center;
  width: 100%;

  .dash {
    margin: 0px 3px;
  }
}

.field-with-mat-inputs {
  margin-bottom: 6px;
}

.btn-typed-option-wrap {
  margin-bottom: 8px;
}

.disable-btn {
  opacity: 0.7;
  pointer-events: none;
}

label {
  margin-bottom: 16px;
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mdc-text-field__input {
      font-size: 14px !important;
    }

    .mat-mdc-select-arrow-wrapper {
      height: 40px !important;
    }
  }

  .instructor-sidenav {
    &.mat-drawer {
      right: 0;
    }
  }
}

.header-tab-with-btn {
  padding: 5px 10px 0px 25px !important;
}

@media (max-width: 990px) {
  .auth-page-with-header {
    height: calc(100vh - 74px) !important;
  }

  .field-wrapper,
  .header-tab-with-btn {
    flex-wrap: wrap;
  }

  .schedule-introductory-lesson-content-wrapper {
    .content-detail-wrapper {
      display: none;
    }

    .schedule-introductory-info-form-wrapper {
      .introductory-lesson-wrapper {
        height: calc(100vh - 154px) !important;
        overflow: auto;

        .schedule-content {
          .schedule-info {
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}
