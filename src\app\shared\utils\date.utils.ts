import moment from 'moment';

export class DateUtils {
  static toUTC(date: string, outputFormat: 'YYYY/MM/DD' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A' | 'yyyy-MM-DDTHH:mm:ss.SSS+0000' | 'yyyy-MM-DDTHH:mm:ss'): string {
    return moment(date).subtract(moment().utcOffset(), 'minutes').format(outputFormat);
  }

  static toLocal(
    date: string | undefined,
    inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000',
    outputFormat = 'yyyy-MM-DDTHH:mm:ss'
  ): string {
    if (!date) {
      return '';
    }
    return moment(date, inputFormat).add(moment().utcOffset(), 'minutes').format(outputFormat);
  }

  static getDateTime(date: string, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): Date {
    return moment(date, outputFormat).toDate();
  }

  static getDateTimeString(date: string | undefined, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): string {
    if (!date) {
      return '';
    }
    return moment(date, outputFormat).toString();
  }

  static getUtcRangeForLocalDate(dateStr: Date | string): { startUtc: string; endUtc: string } {
    if(!dateStr) return { startUtc: '', endUtc: '' };
    const localDate = new Date(dateStr);

    const start = new Date(localDate.setHours(0, 0, 0, 0));
    const end = new Date(localDate.setHours(23, 59, 0, 0));

    return {
      startUtc: moment(start).subtract(moment().utcOffset(), 'minutes').format('yyyy-MM-DDTHH:mm:ss'),
      endUtc: moment(end).subtract(moment().utcOffset(), 'minutes').format('yyyy-MM-DDTHH:mm:ss')
    };
  }
}
