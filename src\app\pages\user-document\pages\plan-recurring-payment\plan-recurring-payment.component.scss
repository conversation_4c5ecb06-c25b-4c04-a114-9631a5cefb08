@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-sidebar-body {
  overflow: auto;
  height: calc(100vh - 70px);
}

.plan-recurring-payment-container {
  .o-card {
    background-color: $gray-bg-light;

    .card-header {
      @include flex-content-space-between;
      margin-bottom: 20px;

      .card-title {
        font-size: 18px;
        font-weight: 700;
        @include flex-content-align-center;

        mat-icon {
          margin-right: 10px;
          color: $primary-color;
          font-size: 22px;
        }
      }
    }

    .plan-details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 25px 100px;
      margin-top: 10px;

      .plan-detail-item {
        .detail-label {
          color: $gray-text;
          margin-bottom: 3px;
          font-weight: 700;
        }

        .detail-value {
          @include ellipse(315px);
          font-size: 16px;
          font-weight: 700;

          &.plan-amount {
            color: $primary-color;
            font-size: 16px;
            font-weight: 700;
          }
        }
      }
    }

    .payment-method-display {
      padding: 20px;
      background-color: $white-color;
      border-radius: 8px;
      border: 2px solid $primary-color;

      .card-number {
        font-size: 17px;
        font-weight: 700;
        width: fit-content;
      }

      .card-exp {
        color: $gray-text;
        font-size: 14px;
      }

      .account-content {
        @include flex-content-space-between;

        .account-section {
          .section-header {
            @include flex-content-align-center;
            font-weight: 600;
            margin-bottom: 12px;

            .section-title {
              font-size: 17px;
            }
          }

          .section-body {
            .detail-row {
              margin-bottom: 6px;

              .detail-label,
              .detail-value {
                font-size: 15px;
              }
            }
          }
        }
      }
    }

    .existing-plans-wrapper {
      overflow: auto;
      max-height: calc(100vh - 435px);

      .existing-plans {
        font-weight: 700;
        
        .detail-value {
          font-size: 16px;
          margin-left: 10px;
        }
  
        .detail-info {
          @include flex-content-align-center;
          color: $gray-text;
          font-size: 14px;
          margin-left: 10px;
          margin-top: 3px;
        }
  
        .plan-amount {
          color: $primary-color;
          font-size: 16px;
        }
      }
    }

    .no-payment-method {
      @include flex-content-align-center;
      padding: 20px;
      background-color: $light-yellow-color;
      border: 1px solid $light-yellow-color;
      border-radius: 8px;
      color: $yellow-color;

      mat-icon {
        margin-right: 10px;
        filter: $yellow-filter;
      }
    }

    .clickable {
      color: $primary-color !important;
      text-decoration: underline;
      cursor: pointer;
    }

    .billing-breakdown {
      .billing-item {
        @include flex-content-space-between;
        padding-bottom: 15px;
        border-bottom: 1px solid $gray-bg-light;

        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        &.discount {
          .billing-value {
            color: $primary-color;
            font-weight: 700;
          }
        }

        &.total {
          padding-top: 15px;
          font-weight: 700;
          font-size: 16px;

          .billing-value {
            color: $primary-color;
          }
        }

        .billing-label {
          color: $gray-text;
          font-weight: 700;
        }

        .billing-value {
          font-size: 16px;
          font-weight: 700;
          color: $original-black-color;
        }
      }
    }
  }

  .payment-note {
    @include flex-content-align-center;
    padding: 10px;
    background-color: $secondary-color;
    border-radius: 8px;

    mat-icon {
      margin-right: 10px;
      font-size: 18px;
    }

    span {
      font-size: 13px;
      color: $gray-text;

      .clickable {
        color: $primary-color;
        cursor: pointer;
        text-decoration: underline;
        font-weight: 600;

        &:hover {
          color: darken($primary-color, 10%);
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 15px;

    .payment-method-display {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  }
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
}
