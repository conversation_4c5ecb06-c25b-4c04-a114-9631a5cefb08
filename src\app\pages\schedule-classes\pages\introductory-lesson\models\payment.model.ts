export interface PaymentParams {
  dependentInformationId?: number;
  classType?: number;
  scheduleId?: number;
  amount?: number;
  ccNum?: string;
  ccExpiry?: string;
  ccType?: string;
  paidDate?: string | Date;
  token?: string;
  planId?: number;
  isSaveCard?: boolean;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  userId?: number;
  customerVaultId?: string;
  firstName?: string;
  lastName?: string;
  isDefault?: boolean;
  customerVoultId?: string;
  transactionType?: number;
  paidAmount?: number;
  totalAmount?: number;
  discountedAmount?: number;
  accountNumber?: string;
  routingNumber?: string;
  accountName?: string;
  planAmount?: number;
  startDate?: Date;
  studentPlanId?: number;
  registrationFees?: number;
  serviceFees?: number;
  chequeNumber?: string;
}

export interface RePaymentParams {
  scheduleId?: number;
  studentId?: number;
}

export interface CardDetailsResponse {
  token?: string;
  number?: string;
  expiry?: string;
  type?: string;
  isSaveCard?: boolean;
  isUsingSavedCard?: boolean;
  customerVaultId?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  firstName?: string;
  lastName?: string;
  transactionType?: number;
  paidAmount?: number;
  totalAmount?: number;
  discountedAmount?: number;
  chequeNumber?: string;
}

export interface AllCustomerCards {
  getAllCardsOfUser: AllCardsOfUser[];
  getAllAchDetailsOfUser: AllAchOfUser[];
}

export interface AllCardsOfUser {
  userId: number;
  firstName: string;
  lastName: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  ccNum: string;
  ccExpiry: string;
  ccType: string;
  isDefault: boolean;
  customerVaultId: string;
}

export interface AllAchOfUser {
  userId: number;
  customerVaultId: string;
  accountNumber: string;
  routingNumber: string;
  accountName: string;
  accountType: string;
  isDefault: boolean;
  firstName: string;
  lastName: string;
  address: string;
  city: string;
  state: string;
  zip: string;
}
