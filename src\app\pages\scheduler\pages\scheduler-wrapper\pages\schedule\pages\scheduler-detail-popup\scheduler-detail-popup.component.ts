import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SchedulerService } from '../../services';
import { AppToasterService, NavigationService } from 'src/app/shared/services';
import { CommonUtils } from 'src/app/shared/utils';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MbscModule } from '@mobiscroll/angular';
import { CancelScheduleFormGroup, ClassTypes, LessonTypes, ScheduleDetailsView } from '../../models';
import { CBGetResponse, MatDialogRes } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import moment from 'moment';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors, Instruments } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';

const DEPENDENCIES = {
  MODULES: [
    MbscModule,
    MatRadioModule,
    CommonModule,
    MatIconModule,
    MatButtonModule,
    SharedModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatTooltipModule,
    FormsModule
  ],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-scheduler-detail-popup',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './scheduler-detail-popup.component.html',
  styleUrl: './scheduler-detail-popup.component.scss'
})
export class SchedulerDetailPopupComponent extends BaseComponent implements OnChanges {
  @Input() selectedEvent!: ScheduleDetailsView | undefined;
  @Input() isFromViewStudent!: boolean;

  scheduleDetail!: ScheduleDetailsView | undefined;
  LESSON_TYPE = LessonTypes;
  classTypes = ClassTypes;
  showAll!: boolean;
  isScheduleRemoved!: boolean | undefined;
  showCancelLessonView = false;
  showUndoBtnLoader = false;
  isUndoCancelView = false;
  cancelScheduleForm!: FormGroup<CancelScheduleFormGroup>;
  currentDateTime = new Date().toUTCString();

  @Output() editLesson = new EventEmitter<ScheduleDetailsView>();
  @Output() closePopup = new EventEmitter<boolean>();

  constructor(
    public readonly schedulerService: SchedulerService,
    private readonly navigationService: NavigationService,
    private readonly toasterService: AppToasterService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedEvent']?.currentValue) {
      if (this.selectedEvent && !this.selectedEvent.isLeave) {
        this.getScheduleDetails(Number(this.selectedEvent?.id));
      }
    }
  }

  initCancelScheduleForm(): void {
    this.cancelScheduleForm = new FormGroup<CancelScheduleFormGroup>({
      classType: new FormControl(this.scheduleDetail?.classType, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      id: new FormControl(this.selectedEvent?.id ? Number(this.selectedEvent?.id) : undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      isRemoveAppointment: new FormControl(true, { nonNullable: true, validators: [Validators.required] }),
      isAllInstances: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      isNotifyClients: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      isPassGenerated: new FormControl(true, { nonNullable: true, validators: [Validators.required] }),
      notes: new FormControl('', { nonNullable: true }),
      studentId: new FormControl(this.scheduleDetail?.studentDetails[0]?.studentId, { nonNullable: true }),
      instrumentId: new FormControl(this.scheduleDetail?.instrumentId, { nonNullable: true })
    });
  }

  getScheduleDetails(id: number): void {
    this.showPageLoader = true;
    this.schedulerService
      .get<CBGetResponse<ScheduleDetailsView>>(`${API_URL.scheduleLessonDetails.getScheduleLessonDetailForView}?id=${id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetailsView>) => {
          this.scheduleDetail = res.result;
          this.isScheduleRemoved = res.result?.isCancelSchedule;
          this.initCancelScheduleForm();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCancelLesson(): void {
    if (this.cancelScheduleForm.invalid) {
      this.cancelScheduleForm.markAllAsTouched();
      return;
    }
    this.cancelScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    this.schedulerService
      .add(
        { ...this.cancelScheduleForm.getRawValue(), currentDateTime: new Date().toUTCString() },
        API_URL.scheduleLessonDetails.cancelLesson
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.showCancelLessonView = false;
          this.onClosePopup(true);
          if (!this.isScheduleRemoved) {
            this.toasterService.success(this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Lesson'));
          } else {
            this.toasterService.success(this.constants.successMessages.removedSuccessfully.replace('{item}', 'Lesson'));
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onUndoCancel(id: number | undefined): void {
    this.showUndoBtnLoader = true;
    const isAllInstances = this.cancelScheduleForm.controls.isAllInstances.value;
    this.schedulerService
      .add({}, `${API_URL.scheduleLessonDetails.undoCancelLesson}?scheduleId=${id}&isAllInstance=${isAllInstances}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onClosePopup(true);
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Schedule'));
          this.showUndoBtnLoader = false;
          this.isUndoCancelView = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showUndoBtnLoader = false;
          this.isUndoCancelView = false;
          this.cdr.detectChanges();
        }
      });
  }

  onAssignMakeUpPassConfirmation(scheduleDetail: ScheduleDetailsView | undefined): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Assign Make Up Pass',
        message: `Are you sure you want to assign ${scheduleDetail?.instrumentName} Lesson make up pass to ${scheduleDetail?.studentDetails[0]?.studentName}?`,
        showReason: true
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onAssignMakeUpPass(scheduleDetail, result.reason);
      }
    });
  }

  onAssignMakeUpPass(scheduleDetail: ScheduleDetailsView | undefined, reason: string | undefined): void {
    this.schedulerService
      .add(
        {
          id: scheduleDetail?.id,
          studentId: scheduleDetail?.studentDetails[0]?.studentId,
          instrumentId: scheduleDetail?.instrumentId,
          classType: scheduleDetail?.classType,
          isRemoveAppointment: false,
          isAllInstances: false,
          isPassGenerated: true,
          notes: reason,
          currentDateTime: new Date().toUTCString()
        },
        API_URL.scheduleLessonDetails.assignMakeUpPass
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.assignedSuccessfully.replace('{item}', 'Make Up Pass'));
          this.onClosePopup(true);
          this.cdr.detectChanges();
        }
      });
  }

  onUpdateNote(notes: Event): void {
    this.scheduleDetail!.notes = (notes.target as HTMLTextAreaElement).value;
    this.schedulerService
      .update(
        {
          id: this.selectedEvent?.id,
          classType: this.selectedEvent?.classType,
          isAllInstances: false,
          scheduleStartDate: this.scheduleDetail?.scheduleDate,
          scheduleEndDate: this.scheduleDetail?.scheduleDate,
          daysOfSchedule: this.selectedEvent?.daysOfSchedule,
          scheduleStartTime: this.scheduleDetail?.start,
          scheduleEndTime: this.scheduleDetail?.end,
          notes: this.scheduleDetail?.notes,
          instructorId: [this.scheduleDetail?.instructorId]
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onClosePopup(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  isWithinNext24Hours(start?: string): boolean {
    const formattedStartDate = moment(new Date(start!));
    const diffInMinutes = formattedStartDate?.diff(new Date(), 'minutes');

    return diffInMinutes > 0 && diffInMinutes <= 1440;
  }

  getInitials(name: string | undefined): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  navigateToViewSchedule(): void {
    if (this.selectedEvent?.id) {
      this.navigationService.navigate([this.path.schedule.root, this.path.crud.view, this.selectedEvent?.id.toString()]);
    }
  }

  onClosePopup(shouldRefreshScheduleData: boolean): void {
    this.closePopup.emit(shouldRefreshScheduleData);
  }

  onEditLesson(scheduleDetail: ScheduleDetailsView | undefined): void {
    this.editLesson.emit(scheduleDetail);
  }

  openInstructorDetails(instructorId: number | undefined): void {
    this.navigationService.navigateToInstructorDetail(instructorId!);
  }

  openStudentDetails(dependentId: number): void {
    this.navigationService.navigateToStudentDetail(dependentId);
  }

  getInstrumentNames(array: Instruments[]) {
    return array
      .slice(1)
      .map(item => item.instrumentName + '(' + item.instrumentGrade + ')')
      .join(', ');
  }

  getInstroctorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }
}
