<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" (click)="closeViewAll()" alt="" />
      <div class="ps-2">
        <div class="title">Past Visits ({{ totalCount }})</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeViewAll()">
        Close
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="auth-page-with-header" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
      <div class="header-tab-with-btn mb-3">
        <div class="tab-item-content">
          @for (attendanceType of attendanceTypes | enumToKeyValue; track $index) {
              <div
                  [ngClass]="{ item: true, 'active-item': selectedTabOption === attendanceType.value }"
                  (click)="setActiveTabOption(attendanceType.value)">
                  {{ attendanceType.key.replace('_', ' ') | titlecase }}
              </div>
          }   
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : pastVisitsList"></ng-container>
    </div>
    @if (totalCount > 10) {
      <pagination-controls
        id="past-visit"
        [previousLabel]="''"
        [nextLabel]="''"
        (pageChange)="onPageChange($event)"
        [responsive]="true"
        class="pagination-controls"></pagination-controls>
    }
  </div>
</div>

<ng-template #pastVisitsList>
    <ng-container [ngTemplateOutlet]="totalCount ? pastVisitList : noDataFound"></ng-container>
</ng-template>

<ng-template #pastVisitList>
  <div class="document-list">
    @for (attendance of studentAttendances | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "past-visit" }; track $index) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div>
            <div class="title" [ngClass]="{ strike: attendance.studentAllAttendance.isCancelled }">
                @if (attendance.studentAllAttendance.isCancelled) {
                  <span>Canceled: </span>
                }
                @switch (attendance.studentAllAttendance.classType) {
                  @case (classTypes.ENSEMBLE_CLASS) {
                    {{ attendance.studentAllAttendance.ensembleClassName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
                  }
                  @case (classTypes.GROUP_CLASS) {
                    {{ attendance.studentAllAttendance.groupClassName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
                  }
                  @case (classTypes.SUMMER_CAMP) {
                    {{ attendance.studentAllAttendance.campName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
                  }
                  @default {
                    {{ attendance.studentAllAttendance.instrumentName }} Lesson ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
                  }  
                }
                @switch (attendance.studentAllAttendance.isPresent) {
                  @case (true) {
                    <img [src]="constants.staticImages.icons.checkCircle" alt="" class="attendance-img" matTooltip="Complete" />
                  }
                  @case (false) {
                    <img [src]="constants.staticImages.icons.noShowBadge" alt="" class="attendance-img no-show" matTooltip="No Show" />
                  }
                  @case (null) {
                    <img [src]="constants.staticImages.icons.timeCircleClock" class="attendance-img incomplete" alt="" matTooltip="Incomplete" />
                  }
                }
            </div>
            <div class="student-content">
                <div class="content-info">{{ attendance.studentAllAttendance.startTime | localDate | date : constants.dateFormats.MMM_d_y }}</div>
                <div class="dot"></div>
                <div class="content-info">{{ attendance.studentAllAttendance.startTime | localDate | date : constants.dateFormats.hh_mm_a }} - {{ attendance.studentAllAttendance.endTime | localDate | date : constants.dateFormats.hh_mm_a }}</div>
                <div class="dot"></div>
                <div class="content-info">{{ attendance.studentAllAttendance.locationName }}</div>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h5 class="mb-0">NO ATTENDANCE AVAILABLE!</h5>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
